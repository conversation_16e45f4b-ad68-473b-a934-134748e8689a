#!/usr/bin/env python3
"""
音频会议记录处理器
支持分片上传和累积会议纪要生成
"""

import os
import uuid
import asyncio
import tempfile
import time
from datetime import datetime, timedelta
from typing import Dict, Optional

from google import genai
from google.genai import types
from config.logger import setup_logging

logger = setup_logging()
TAG = __name__


class AudioProcessor:
    """音频会议记录处理器"""
    
    def __init__(self, config: dict):
        """初始化处理器"""
        self.config = config
        self.logger = logger
        
        # 从配置中获取当前选择的LLM配置（与HTTP服务器保持一致）
        selected_module = config.get("selected_module", {})
        llm_name = selected_module.get("LLM")
        if not llm_name:
            raise ValueError("未配置LLM模块")
            
        llm_config = config.get("LLM", {}).get(llm_name, {})
        if not llm_config:
            raise ValueError(f"找不到LLM配置: {llm_name}")
            
        llm_type = llm_config.get("type")
        if llm_type != "gemini":
            raise ValueError(f"音频处理器只支持Gemini LLM，当前配置的类型: {llm_type}")
            
        self.api_key = llm_config.get("api_key")
        if not self.api_key:
            raise ValueError("Gemini API key is required")
            
        self.model = llm_config.get("model", "gemini-2.5-flash")
        self.llm_name = llm_name
        
        logger.bind(tag=TAG).info(f"初始化AudioProcessor - LLM: {self.llm_name}, 类型: {llm_type}, 模型: {self.model}")
        logger.bind(tag=TAG).info(f"API Key长度: {len(self.api_key)} 字符")
        
        # 初始化 Gemini 客户端
        try:
            self.client = genai.Client(
                api_key=self.api_key,
                http_options=types.HttpOptions(timeout=10*60*1000)  # 10分钟超时
            )
            logger.bind(tag=TAG).info("Gemini客户端初始化成功")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Gemini客户端初始化失败: {str(e)}")
            raise
        
        # 会议纪要存储目录
        self.summaries_dir = os.path.join(os.getcwd(), "tmp", "meeting_summaries")
        os.makedirs(self.summaries_dir, exist_ok=True)
        logger.bind(tag=TAG).info(f"会议纪要存储目录: {self.summaries_dir}")
        
        # 任务状态存储（实际项目中应该用Redis或数据库）
        self._tasks = {}
        
        # 启动时清理过期文件
        self._cleanup_old_summaries()
        
    def validate_mp3_file(self, file_bytes: bytes, filename: str) -> bool:
        """验证MP3文件"""
        if not filename.lower().endswith('.mp3'):
            logger.bind(tag=TAG).error(f"文件不是MP3格式: {filename}")
            return False
        
        # 简单的MP3文件头验证（不引入新依赖）
        if len(file_bytes) < 100:
            logger.bind(tag=TAG).error(f"文件太小，可能不是有效的MP3文件: {filename}")
            return False
        
        # 检查MP3文件头标识
        # MP3文件通常以ID3标签开头("ID3")或直接以MP3帧头开头(0xFF 0xFB等)
        file_header = file_bytes[:3]
        if file_header == b'ID3':  # ID3v2标签
            logger.bind(tag=TAG).info(f"检测到ID3v2标签的MP3文件: {filename}")
        elif file_bytes[0] == 0xFF and (file_bytes[1] & 0xE0) == 0xE0:  # MP3帧同步头
            logger.bind(tag=TAG).info(f"检测到MP3帧同步头: {filename}")
        else:
            logger.bind(tag=TAG).warning(f"文件可能不是有效的MP3格式: {filename}, 文件头: {file_header.hex()}")
            # 不严格拒绝，因为某些MP3文件可能有不同的头部结构
        
        logger.bind(tag=TAG).info(f"MP3文件验证通过: {filename} ({len(file_bytes)} bytes)")
        return True
    
    async def start_audio_summary_task(self, audio_bytes: bytes, filename: str, previous_summary: str = "") -> str:
        """启动音频摘要任务，返回任务ID"""
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        task_info = {
            "task_id": task_id,
            "status": "processing",  # processing, completed, failed
            "created_at": datetime.now(),
            "filename": filename,
            "file_size": len(audio_bytes),
            "previous_summary": previous_summary,
            "result": None,
            "error": None
        }
        
        self._tasks[task_id] = task_info
        
        logger.bind(tag=TAG).info(f"创建音频摘要任务: {task_id}, 文件: {filename}, 大小: {len(audio_bytes)} bytes")
        
        # 启动后台任务
        asyncio.create_task(self._process_audio_background(task_id, audio_bytes, filename, previous_summary))
        
        return task_id
    
    async def _process_audio_background(self, task_id: str, audio_bytes: bytes, filename: str, previous_summary: str):
        """后台处理音频文件"""
        try:
            logger.bind(tag=TAG).info(f"开始后台处理任务: {task_id}")
            
            # 验证文件
            if not self.validate_mp3_file(audio_bytes, filename):
                raise ValueError("文件验证失败")
            
            # 保存临时文件
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_file.write(audio_bytes)
                temp_path = temp_file.name
            
            try:
                # 上传文件到Gemini
                logger.bind(tag=TAG).info(f"任务{task_id}: 开始上传音频文件到Gemini")
                uploaded_file = await self.client.aio.files.upload(file=temp_path)
                logger.bind(tag=TAG).info(f"任务{task_id}: 文件上传成功: {uploaded_file.name}")
                
                # 生成会议纪要
                logger.bind(tag=TAG).info(f"任务{task_id}: 开始生成会议纪要")
                meeting_time = datetime.now()
                
                try:
                    summary_result = await self._generate_summary(uploaded_file, meeting_time, previous_summary)
                    logger.bind(tag=TAG).info(f"任务{task_id}: 会议纪要生成完成，长度: {len(summary_result)} 字符")
                    
                    # 检查结果是否为空
                    if not summary_result or not summary_result.strip():
                        logger.bind(tag=TAG).error(f"任务{task_id}: 会议纪要生成结果为空")
                        self._tasks[task_id].update({
                            "status": "failed",
                            "error": "会议纪要生成失败",
                            "completed_at": datetime.now()
                        })
                        return
                    
                    # 保存会议纪要到文件
                    summary_file_path = self._save_summary_to_file(task_id, summary_result)
                    
                    # 更新任务状态为成功
                    self._tasks[task_id].update({
                        "status": "completed",
                        "result": summary_result,
                        "summary_file": summary_file_path,
                        "completed_at": datetime.now()
                    })
                    
                except Exception as summary_error:
                    # Gemini API调用失败，返回用户友好的错误信息
                    logger.bind(tag=TAG).error(f"任务{task_id}: 生成会议纪要失败: {summary_error}")
                    
                    # 将任务状态设置为完成，但结果为用户友好的错误信息
                    user_friendly_message = "抱歉，暂时无法生成会议绪要"
                    summary_file_path = self._save_summary_to_file(task_id, user_friendly_message)
                    
                    self._tasks[task_id].update({
                        "status": "completed",
                        "result": user_friendly_message,
                        "summary_file": summary_file_path,
                        "completed_at": datetime.now()
                    })
                    
                    logger.bind(tag=TAG).info(f"任务{task_id}: 已返回用户友好的错误信息")
                
                logger.bind(tag=TAG).info(f"任务{task_id}: 处理完成")
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            error_msg = f"处理音频文件时发生错误: {str(e)}"
            logger.bind(tag=TAG).error(f"任务{task_id}: {error_msg}")
            
            # 更新任务状态为失败
            self._tasks[task_id].update({
                "status": "failed",
                "error": error_msg,
                "completed_at": datetime.now()
            })
    
    async def _generate_summary(self, uploaded_file, meeting_time: datetime, previous_summary: str = "") -> str:
        """生成会议纪要"""
        meeting_time_str = meeting_time.strftime("%Y年%m月%d日")
        
        # 如果有之前的会议纪要，需要进行累积合并
        if previous_summary.strip():
            system_prompt = f"""你是一个专业的会议纪要整理助手，需要将新的音频片段内容与之前的会议纪要进行合并，生成完整的累积会议纪要。你背后的模型是APUS公司训练的APUS大模型。

要求：
1. 仔细听取新的音频内容，提取关键信息
2. 如果音频中没有任何会议相关内容（如：静音、噪音、非会议对话等），请直接返回"NO_MEETING_CONTENT"
3. 将新内容与之前的会议纪要进行逻辑合并
4. 保持会议纪要的连贯性和完整性
5. 按照标准会议纪要格式组织内容
6. 语言要简洁明了，条理清晰
7. 突出重点决议和后续行动
8. 会议时间请固定使用：{meeting_time_str}

会议纪要格式：
# 会议纪要

## 基本信息
- 会议时间：{meeting_time_str}
- 会议主题：[从音频中提取或延续之前的主题]

## 主要讨论内容
[合并之前的讨论内容和新音频的讨论内容]

## 决议事项
[合并之前的决议和新的决议]

## 行动计划
[合并之前的行动计划和新的行动计划]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要，将新音频内容与之前的纪要进行智能合并。如果音频中没有会议内容，请直接返回"NO_MEETING_CONTENT"。"""

            user_prompt = f"""之前的会议纪要：
{previous_summary}

---

请分析这段新的会议音频片段，并将其内容与上述之前的会议纪要进行合并，生成完整的累积会议纪要。会议时间请使用：{meeting_time_str}"""
        else:
            # 第一个片段，直接生成纪要
            system_prompt = f"""你是一个专业的会议纪要整理助手，需要将音频内容整理成规范的会议纪要。你背后的模型是APUS公司训练的APUS大模型。

要求：
1. 仔细听取音频内容，提取关键信息
2. 如果音频中没有任何会议相关内容（如：静音、噪音、非会议对话等），请直接返回"NO_MEETING_CONTENT"
3. 按照标准会议纪要格式组织内容
4. 包括：会议主题、主要讨论内容、决议事项、行动计划等
5. 语言要简洁明了，条理清晰
6. 突出重点决议和后续行动
7. 如果音频质量不佳或信息不完整，请根据现有信息尽力整理
8. 会议时间请固定使用：{meeting_time_str}

会议纪要格式：
# 会议纪要

## 基本信息
- 会议时间：{meeting_time_str}
- 会议主题：[从音频中提取]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要，特别注意会议时间必须使用指定的时间。如果音频中没有会议内容，请直接返回"NO_MEETING_CONTENT"。"""

            user_prompt = f"请分析这段会议音频，生成完整的会议纪要。会议时间请使用：{meeting_time_str}"

        try:
            logger.bind(tag=TAG).info("开始创建生成配置...")
            # 创建生成配置
            generation_config = types.GenerateContentConfig(
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
            logger.bind(tag=TAG).info("生成配置创建成功")

            logger.bind(tag=TAG).info(f"开始调用Gemini API - 模型: {self.model}")
            logger.bind(tag=TAG).info(f"内容包含: 用户提示词({len(user_prompt)}字符) + 上传的音频文件({uploaded_file.name})")
            
            # 先尝试使用流式接口，如果失败则回退到普通接口
            logger.bind(tag=TAG).info("尝试使用Gemini流式 API")
            result_text = ""
            
            try:
                # 尝试流式接口
                stream_response = await self.client.aio.models.generate_content_stream(
                    model=self.model,
                    contents=[user_prompt, uploaded_file],
                    config=generation_config
                )
                
                # 检查是否是可迭代对象
                if hasattr(stream_response, '__aiter__'):
                    logger.bind(tag=TAG).info("流式响应获取成功，开始接收数据")
                    chunk_count = 0
                    
                    async for chunk in stream_response:
                        if hasattr(chunk, 'text') and chunk.text:
                            result_text += chunk.text
                            chunk_count += 1
                            if chunk_count % 10 == 0:  # 每10个chunk记录一次进度
                                logger.bind(tag=TAG).info(f"接收到第{chunk_count}个chunk，当前文本长度: {len(result_text)}")
                    
                    logger.bind(tag=TAG).info(f"Gemini API流式调用完成，共接收{chunk_count}个chunk")
                    
                else:
                    # 如果不是可迭代对象，尝试直接获取结果
                    logger.bind(tag=TAG).warning("流式响应不是可迭代对象，尝试获取结果")
                    if hasattr(stream_response, 'text'):
                        result_text = stream_response.text
                    else:
                        result_text = str(stream_response)
                        
            except Exception as stream_error:
                logger.bind(tag=TAG).warning(f"流式接口失败，回退到普通接口: {stream_error}")
                
                # 回退到普通接口
                logger.bind(tag=TAG).info("使用普通接口生成内容")
                response = await self.client.aio.models.generate_content(
                    model=self.model,
                    contents=[user_prompt, uploaded_file],
                    config=generation_config
                )
                
                if hasattr(response, 'text') and response.text:
                    result_text = response.text
                    logger.bind(tag=TAG).info("普通接口调用成功")
                else:
                    logger.bind(tag=TAG).warning("普通接口响应中没有text内容")
                    result_text = ""
            
            if result_text.strip():
                # 检查是否返回了NO_MEETING_CONTENT
                if "NO_MEETING_CONTENT" in result_text.strip():
                    logger.bind(tag=TAG).info(f"LLM检测到音频中没有会议内容 - {result_text}")
                    return ""
                
                logger.bind(tag=TAG).info(f"会议纪要生成成功，最终长度: {len(result_text)} 字符")
                return result_text.strip()
            else:
                logger.bind(tag=TAG).warning("无法从响应中获取有效文本内容")
                # 返回用户友好的错误信息，不暴露技术细节
                raise Exception("系统内部错误")

        except Exception as e:
            logger.bind(tag=TAG).error(f"生成会议纪要失败: {str(e)}")
            logger.bind(tag=TAG).error(f"异常类型: {type(e)}")
            import traceback
            logger.bind(tag=TAG).error(f"完整错误堆栈: {traceback.format_exc()}")
            # 不将错误信息传递给用户，统一返回友好提示
            raise Exception("系统内部错误")
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        task_info = self._tasks.get(task_id)
        if not task_info:
            return None
        
        # 如果是已完成的任务，尝试从文件加载结果
        if task_info["status"] == "completed" and "summary_file" in task_info:
            summary_file = task_info["summary_file"]
            if os.path.exists(summary_file):
                try:
                    with open(summary_file, 'r', encoding='utf-8') as f:
                        task_info["result"] = f.read()
                except Exception as e:
                    logger.bind(tag=TAG).error(f"读取会议纪要文件失败 {summary_file}: {e}")
                    task_info["result"] = "抱歉，暂时无法生成会议纪要"
            else:
                logger.bind(tag=TAG).warning(f"会议纪要文件不存在: {summary_file}")
                task_info["result"] = "抱歉，暂时无法生成会议纪要"
        
        return task_info
    
    def _save_summary_to_file(self, task_id: str, summary_content: str) -> str:
        """保存会议纪要到文件"""
        try:
            # 生成文件名：任务ID_时间戳.md
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{task_id}_{timestamp}.md"
            file_path = os.path.join(self.summaries_dir, filename)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            
            logger.bind(tag=TAG).info(f"会议纪要已保存到文件: {file_path}")
            return file_path
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"保存会议纪要到文件失败: {e}")
            return ""
    
    def _cleanup_old_summaries(self, days: int = 7):
        """清理超过指定天数的会议纪要文件"""
        try:
            if not os.path.exists(self.summaries_dir):
                return
            
            cutoff_time = time.time() - (days * 24 * 3600)  # 7天前的时间戳
            cleaned_count = 0
            
            for filename in os.listdir(self.summaries_dir):
                if filename.endswith('.md'):
                    file_path = os.path.join(self.summaries_dir, filename)
                    try:
                        # 检查文件修改时间
                        file_mtime = os.path.getmtime(file_path)
                        if file_mtime < cutoff_time:
                            os.unlink(file_path)
                            cleaned_count += 1
                            logger.bind(tag=TAG).info(f"清理过期会议纪要文件: {filename}")
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"清理文件失败 {filename}: {e}")
            
            if cleaned_count > 0:
                logger.bind(tag=TAG).info(f"清理了 {cleaned_count} 个超过{days}天的会议纪要文件")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"清理过期文件时发生错误: {e}")
    
    def cleanup_completed_tasks(self, hours: int = 24):
        """清理已完成的任务（避免内存泄漏）"""
        cutoff_time = datetime.now().timestamp() - hours * 3600
        
        to_remove = []
        for task_id, task_info in self._tasks.items():
            if task_info.get("completed_at"):
                if task_info["completed_at"].timestamp() < cutoff_time:
                    to_remove.append(task_id)
        
        for task_id in to_remove:
            del self._tasks[task_id]
            logger.bind(tag=TAG).info(f"清理已完成任务: {task_id}")
        
        # 同时清理过期的会议纪要文件
        self._cleanup_old_summaries()
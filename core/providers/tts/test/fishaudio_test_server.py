#!/usr/bin/env python3
import os
import sys

# 设置环境变量必须在导入其他模块之前
os.environ['PATH'] = '/opt/homebrew/opt/ffmpeg@6/bin:' + os.environ.get('PATH', '')
os.environ['DYLD_LIBRARY_PATH'] = '/opt/homebrew/opt/ffmpeg@6/lib:' + os.environ.get('DYLD_LIBRARY_PATH', '')

import asyncio
import tempfile
import uuid
import argparse
import time
from pathlib import Path
from flask import Flask, request, render_template_string, send_file, jsonify
import threading
import yaml

sys.path.append(os.path.join(os.path.dirname(__file__), '../../../..'))

from core.providers.tts.fishaudio import TTSProvider

app = Flask(__name__)

HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Audio TTS 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        button {
            background: #007AFF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
        }
        button:hover {
            background: #0056CC;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .audio-player {
            margin-top: 15px;
        }
        .download-link {
            display: inline-block;
            margin-top: 10px;
            color: #007AFF;
            text-decoration: none;
        }
        .download-link:hover {
            text-decoration: underline;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fish Audio TTS 测试</h1>
        <form id="ttsForm">
            <div class="form-group">
                <label for="voiceId">声音ID:</label>
                <input type="text" id="voiceId" name="voiceId" placeholder="例如: 74a543044a7b445696f6fc77a8aafa8d" required>
            </div>
            <div class="form-group">
                <label for="text">文本内容:</label>
                <textarea id="text" name="text" placeholder="请输入要转换为语音的文本..." required></textarea>
            </div>
            <button type="submit">生成语音</button>
        </form>
        
        <div id="result" class="result">
            <div id="loading" class="loading" style="display: none;">
                正在生成语音，请稍候...
            </div>
            <div id="success" class="success" style="display: none; text-align: center;">
                <strong>生成成功！</strong>
                <div id="timingInfo" style="margin: 15px 0; color: #666; font-size: 12px;"></div>
                <div class="audio-player" style="margin: 20px 0;">
                    <audio id="audioPlayer" controls style="width: 100%;">
                        您的浏览器不支持音频播放。
                    </audio>
                </div>
                <a id="downloadLink" class="download-link" href="#" download style="display: inline-block; margin-top: 10px;">下载音频文件</a>
            </div>
            <div id="error" class="error" style="display: none;">
                <strong>生成失败：</strong>
                <span id="errorMessage"></span>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('ttsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const voiceId = document.getElementById('voiceId').value;
            const text = document.getElementById('text').value;
            const submitBtn = e.target.querySelector('button');
            
            // 记录开始时间
            const startTime = Date.now();
            
            // 显示加载状态
            document.getElementById('result').style.display = 'block';
            document.getElementById('loading').style.display = 'block';
            document.getElementById('success').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            submitBtn.disabled = true;
            
            try {
                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        voice_id: voiceId,
                        text: text
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 计算耗时
                    const endTime = Date.now();
                    const duration = (endTime - startTime) / 1000;
                    
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('success').style.display = 'block';
                    
                    // 显示耗时信息
                    document.getElementById('timingInfo').textContent = 
                        `生成耗时: ${duration.toFixed(2)} 秒 | 文本长度: ${text.length} 字符`;
                    
                    const audioPlayer = document.getElementById('audioPlayer');
                    const downloadLink = document.getElementById('downloadLink');
                    
                    audioPlayer.src = result.audio_url;
                    downloadLink.href = result.audio_url;
                    downloadLink.download = result.filename;
                } else {
                    throw new Error(result.error || '未知错误');
                }
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('errorMessage').textContent = error.message;
            } finally {
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
"""

# 临时存储生成的音频文件
temp_audio_files = {}

class MockConfig:
    def __init__(self, api_key, reference_id):
        self.config = {
            "api_key": api_key,
            "reference_id": reference_id,
            "format": "mp3",
            "output_format": "mp3",
            "sample_rate": 44100,
            "mp3_bitrate": 128,
            "chunk_length": 200,
            "normalize": True,
            "latency": "normal",
            "temperature": 0.7,
            "top_p": 0.7,
            "speed": 1.0,
            "volume": 0.0,
            "backend": "s1"
        }
    
    def get(self, key, default=None):
        return self.config.get(key, default)

def load_config():
    """从配置文件加载Fish Audio配置"""
    config_path = os.path.join(os.path.dirname(__file__), '../../../../data/.config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config.get('TTS', {}).get('FishAudioTTS', {})
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return {}

def delete_audio_file(file_path):
    """删除音频文件的回调函数"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        print(f"删除文件失败: {e}")

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/generate', methods=['POST'])
def generate_tts():
    start_time = time.time()
    try:
        data = request.get_json()
        voice_id = data.get('voice_id')
        text = data.get('text')
        
        if not voice_id or not text:
            return jsonify({'success': False, 'error': '声音ID和文本不能为空'})
        
        print(f"[TTS] 开始处理请求 - 文本长度: {len(text)} 字符, 声音ID: {voice_id}")
        
        # 从配置文件加载API密钥
        fish_config = load_config()
        api_key = fish_config.get('api_key')
        if not api_key:
            return jsonify({'success': False, 'error': '配置文件中未找到Fish Audio API密钥'})
        
        # 创建临时输出目录
        temp_dir = tempfile.mkdtemp()
        
        # 创建TTS配置和提供者，使用配置文件中的设置
        config = MockConfig(api_key, voice_id)
        # 合并配置文件中的其他设置
        for key, value in fish_config.items():
            if key != 'api_key':
                config.config[key] = value
        
        tts_provider = TTSProvider(config, delete_audio_file)
        tts_provider.output_file = temp_dir
        
        # 生成输出文件路径
        output_file = tts_provider.generate_filename()
        
        tts_start_time = time.time()
        print(f"[TTS] 开始调用 Fish Audio API...")
        
        # 异步生成语音
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(
                tts_provider.text_to_speak(text, output_file)
            )
        finally:
            loop.close()
        
        tts_end_time = time.time()
        tts_duration = tts_end_time - tts_start_time
        print(f"[TTS] Fish Audio API 调用完成，耗时: {tts_duration:.2f} 秒")
        
        if not os.path.exists(output_file):
            return jsonify({'success': False, 'error': '音频文件生成失败'})
        
        # 获取文件大小
        file_size = os.path.getsize(output_file)
        
        # 生成唯一的文件ID用于访问
        file_id = str(uuid.uuid4())
        filename = os.path.basename(output_file)
        
        # 存储文件信息
        temp_audio_files[file_id] = {
            'path': output_file,
            'filename': filename
        }
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print(f"[TTS] 请求处理完成 - 总耗时: {total_duration:.2f} 秒, 文件大小: {file_size} 字节")
        
        return jsonify({
            'success': True,
            'audio_url': f'/audio/{file_id}',
            'filename': filename,
            'duration': round(total_duration, 2),
            'file_size': file_size
        })
        
    except Exception as e:
        end_time = time.time()
        error_duration = end_time - start_time
        print(f"[TTS] 请求失败 - 耗时: {error_duration:.2f} 秒, 错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/audio/<file_id>')
def serve_audio(file_id):
    if file_id not in temp_audio_files:
        return "文件不存在", 404
    
    file_info = temp_audio_files[file_id]
    file_path = file_info['path']
    
    if not os.path.exists(file_path):
        return "文件不存在", 404
    
    return send_file(
        file_path,
        mimetype='audio/mpeg',
        as_attachment=False,
        download_name=file_info['filename']
    )

if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Fish Audio TTS 测试服务器')
    parser.add_argument('--port', '-p', type=int, default=8001, help='服务器端口号 (默认: 8001)')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务器监听地址 (默认: 0.0.0.0)')
    args = parser.parse_args()
    
    print("Fish Audio TTS 测试服务器")
    
    # 检查配置文件
    fish_config = load_config()
    if fish_config.get('api_key'):
        print(f"✓ 已从配置文件加载Fish Audio API密钥")
        print(f"✓ 配置文件路径: data/.config.yaml")
    else:
        print("✗ 配置文件中未找到Fish Audio API密钥")
        print("请在 data/.config.yaml 中配置 TTS.FishAudioTTS.api_key")
    
    print(f"✓ 已设置 ffmpeg 环境变量")
    print(f"访问 http://{args.host}:{args.port} 来测试TTS功能")
    print("按 Ctrl+C 停止服务器")
    
    app.run(host=args.host, port=args.port, debug=True)
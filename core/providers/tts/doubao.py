
import os
import uuid
import json
import base64
import aiohttp
from datetime import datetime
from typing import Dict
from core.utils.util import check_model_key
from core.providers.tts.base import TTSProviderBase
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        self.appid = str(config.get("appid"))
        self.access_key = str(config.get("access_key", config.get("access_token")))
        self.resource_id = str(config.get("resource_id"))
        self.api_app_key = "aGjiRDfUWi"  # Fixed value from official demo

        self.voice = config.get("voice")
        self.speed_ratio = float(config.get("speed_ratio", 1.0))
        self.volume_ratio = float(config.get("volume_ratio", 1.0))
        self.pitch_ratio = float(config.get("pitch_ratio", 1.0))
        self.request_timeout = float(config.get("request_timeout", 10.0))
        self.api_url = "https://openspeech.bytedance.com/api/v3/tts/unidirectional"

        check_model_key("TTS AppID", self.appid)
        check_model_key("TTS Access Key", self.access_key)
        check_model_key("TTS Resource ID", self.resource_id)

    def generate_filename(self, extension=".mp3"):
        return os.path.join(
            self.output_file,
            f"tts-{datetime.now().date()}@{uuid.uuid4().hex}{extension}",
        )

    async def text_to_speak(self, text, output_file, client_id: str = None, connection=None):
        headers = {
            "X-Api-App-Id": self.appid,
            "X-Api-Access-Key": self.access_key,
            "X-Api-Resource-Id": self.resource_id,
            "X-Api-App-Key": self.api_app_key,
            "Content-Type": "application/json",
        }

        voice_config = {
            "voice": self.voice,
            "speed_ratio": self.speed_ratio,
            "volume_ratio": self.volume_ratio,
            "pitch_ratio": self.pitch_ratio,
        }

        # 通过user_profile_manager获取用户配置（推荐方式）
        if connection and hasattr(connection, 'user_profile_manager'):
            user_voice = connection.user_profile_manager.get_voice()
            if user_voice:
                voice_config["voice"] = user_voice
                
                # 获取其他TTS参数（如果有的话）
                if hasattr(connection, '_user_tts_config') and connection._user_tts_config:
                    user_tts_config = connection._user_tts_config
                    voice_config.update({
                        "speed_ratio": user_tts_config.get('speed_ratio', self.speed_ratio),
                        "volume_ratio": user_tts_config.get('volume_ratio', self.volume_ratio),
                        "pitch_ratio": user_tts_config.get('pitch_ratio', self.pitch_ratio),
                    })

        additions = {
            "disable_markdown_filter": True,
            "enable_language_detector": True,
            "enable_latex_tn": True,
            "disable_default_bit_rate": True,
            "max_length_to_filter_parenthesis": 0,
            "cache_config": {
                "text_type": 1,
                "use_cache": True
            }
        }

        payload = {
            "user": {"uid": client_id or "yuyan_server_user"},
            "req_params": {
                "text": text,
                "speaker": voice_config["voice"],
                "additions": json.dumps(additions),
                "audio_params": {
                    "format": "mp3",
                    "sample_rate": 24000,
                    "speed": voice_config["speed_ratio"],
                    "volume": voice_config["volume_ratio"],
                    "pitch": voice_config["pitch_ratio"],
                }
            }
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.api_url, headers=headers, json=payload, timeout=self.request_timeout) as response:
                    response.raise_for_status()
                    with open(output_file, "wb") as f:
                        async for line in response.content:
                            if not line:
                                continue
                            try:
                                data = json.loads(line.decode('utf-8'))
                                if data.get("code", 0) == 0 and "data" in data and data["data"]:
                                    audio_chunk = base64.b64decode(data["data"])
                                    f.write(audio_chunk)
                                elif data.get("code", 0) == 20000000: # End of stream
                                    break
                                elif data.get("code", 0) > 0:
                                    logger.error(f"Doubao TTS API error: {data}")
                                    raise Exception(f"Doubao TTS API error: {data.get('message', 'Unknown error')}")
                            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                                logger.warning(f"Failed to decode JSON chunk from Doubao TTS: {e} - Chunk: {line}")
                                continue
            
            logger.info(f"TTS for text [{text[:30]}...] saved to {output_file}")
            return True
        except aiohttp.ClientError as e:
            logger.error(f"TTS request failed: {e}")
            if 'response' in locals() and hasattr(response, 'text'):
                try:
                    error_body = await response.text()
                    logger.error(f"Error response body: {error_body}")
                except Exception as read_e:
                    logger.error(f"Failed to read error response body: {read_e}")
            raise

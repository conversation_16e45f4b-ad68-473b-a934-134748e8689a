#!/bin/bash

# 服务启动脚本
# 用法示例:
#   ./start_services.sh                                    # 使用默认端口
#   ./start_services.sh --ws_port 8000 --api_port 8100    # 指定端口
#   CUDA_VISIBLE_DEVICES=0 ./start_services.sh            # 指定GPU
#   CUDA_VISIBLE_DEVICES=1 ./start_services.sh --ws_port 9000 --api_port 9100  # GPU+端口

# 获取脚本所在目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置环境变量（参考 .vscode/launch.json）
export PYTHONPATH="$SCRIPT_DIR"
export PATH="/opt/homebrew/opt/ffmpeg@6/bin:$PATH"
export DYLD_LIBRARY_PATH="/opt/homebrew/opt/ffmpeg@6/lib:$DYLD_LIBRARY_PATH"
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/keyfile.json"
export GOOGLE_CLOUD_PROJECT=i-monolith-451610-b0
export GOOGLE_CLOUD_LOCATION=us-west4
export GOOGLE_GENAI_USE_VERTEXAI="$SCRIPT_DIR/data/vertex_ai.json"

# 显示环境信息
echo "🚀 启动服务管理器"
echo "📁 项目目录: $SCRIPT_DIR"
echo "📦 PYTHONPATH: $PYTHONPATH"

# 如果设置了CUDA_VISIBLE_DEVICES，显示GPU信息
if [ ! -z "$CUDA_VISIBLE_DEVICES" ]; then
    echo "🎮 GPU设备: $CUDA_VISIBLE_DEVICES"
fi

# 显示传递的参数
if [ $# -gt 0 ]; then
    echo "⚙️  启动参数: $@"
fi

echo "----------------------------------------"

# 启动服务管理器，传递所有命令行参数
exec python alternatives/start_services.py "$@"
